/* Reset và Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Arial", sans-serif;
  background-color: #0a0a0a;
  color: #ffffff;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.header {
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  padding: 1rem 0;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  background: linear-gradient(45deg, #ff6b6b, #ffa500);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  font-size: 1.8rem;
}

.nav ul {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav a {
  color: #ffffff;
  text-decoration: none;
  transition: color 0.3s;
}

.nav a:hover {
  color: #ff6b6b;
}

.search-box {
  display: flex;
  gap: 0.5rem;
}

.search-box input {
  padding: 0.5rem;
  border: none;
  border-radius: 5px;
  background: #333;
  color: white;
  width: 200px;
}

.search-box button {
  padding: 0.5rem 1rem;
  background: linear-gradient(45deg, #ff6b6b, #ffa500);
  border: none;
  border-radius: 5px;
  color: white;
  cursor: pointer;
  transition: transform 0.3s;
}

.search-box button:hover {
  transform: scale(1.05);
}

/* Hero Section */
.hero {
  height: 100vh;
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)),
    url("https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?ixlib=rb-4.0.3")
      center/cover;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.hero-content h2 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: #ffffff;
  font-weight: normal;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #cccccc;
}

.cta-btn {
  padding: 1rem 2rem;
  background: linear-gradient(45deg, #ff6b6b, #ffa500);
  border: none;
  border-radius: 25px;
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  transition: transform 0.3s;
}

.cta-btn:hover {
  transform: scale(1.05);
}

/* Categories Section */
.categories {
  padding: 4rem 0;
  background: #111111;
}

.categories h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  color: #ffffff;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.category-item {
  background: linear-gradient(135deg, #2d2d2d, #1a1a1a);
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
  border: 2px solid transparent;
}

.category-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
  border-color: #1145d3;
}

.category-item i {
  font-size: 3rem;
  color: #ff6b6b;
  margin-bottom: 1rem;
}

.category-item span {
  display: block;
  font-size: 1.2rem;
  font-weight: bold;
}

/* Movies Section */
.movies-section {
  padding: 4rem 0;
  background: #0a0a0a;
}

.movies-section h2 {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  color: #ffffff;
}

.movies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
}

.movie-card {
  background: linear-gradient(135deg, #2d2d2d, #1a1a1a);
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
  border: 2px solid transparent;
}

.movie-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(255, 107, 107, 0.3);
  border-color: #d6c6c6;
}

.movie-poster {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.movie-info {
  padding: 1.5rem;
}

.movie-title {
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #ffffff;
}

.movie-genre {
  color: #cccccc;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.movie-duration {
  color: #ffa500;
  font-size: 0.8rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
}

.modal-content {
  background: linear-gradient(135deg, #2d2d2d, #1a1a1a);
  margin: 5% auto;
  padding: 2rem;
  border-radius: 15px;
  width: 90%;
  max-width: 800px;
  position: relative;
}

.close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  position: absolute;
  right: 20px;
  top: 15px;
}

.close:hover {
  color: #ff6b6b;
}

.video-container {
  margin-bottom: 2rem;
}

.video-container video {
  width: 100%;
  border-radius: 10px;
}

.movie-info h3 {
  color: #ffffff;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.movie-info p {
  color: #cccccc;
  line-height: 1.6;
}

/* Footer */
.footer {
  background: #1a1a1a;
  padding: 2rem 0;
  text-align: center;
  color: #cccccc;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header .container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav ul {
    gap: 1rem;
  }

  .search-box input {
    width: 150px;
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .category-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .movies-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .modal-content {
    width: 95%;
    margin: 10% auto;
    padding: 1rem;
  }
}
