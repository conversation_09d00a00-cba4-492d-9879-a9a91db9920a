// Sample movie data
const movies = [
  {
    id: 1,
    title:
      "Phim Hài Việt Nam Chiếu Rload - L<PERSON>y Vợ Sài Gòn Full HD - Hai <PERSON> , <PERSON>h<PERSON><PERSON>a",
    year: 2019,
    rating: 8.4,
    genre: "Hành động",
    duration: "95 phút",
    poster:
      "https://i.ytimg.com/vi/vjX7BLY3Skk/hq720.jpg?sqp=-oaymwEnCNAFEJQDSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&rs=AOn4CLBjCDj7drVLdqlDakeL3imkt_FP2g",
    video: "https://helvid.net/play/index/4442e239e653.mp4",
    description: "Thanos và khôi phục lại trật tự vũ trụ.",
    category: "action",
  },

  {
    id: 2,
    title: "Spider-Man: No Way Home",
    year: 2021,
    genre: "<PERSON><PERSON><PERSON> đ<PERSON>, <PERSON><PERSON><PERSON> l<PERSON>",
    rating: 8.2,
    duration: "148 phút",
    poster:
      "https://images.unsplash.com/photo-1626814026160-2237a95fc5a0?ixlib=rb-4.0.3&w=400",
    video:
      "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
    description:
      "Peter Parker phải đối mặt với hậu quả khi danh tính Spider-Man của anh bị tiết lộ. Khi anh nhờ Doctor Strange giúp đỡ, phép thuật trở nên nguy hiểm và buộc Parker phải khám phá ý nghĩa thực sự của việc trở thành Spider-Man.",
    category: "action",
  },
  {
    id: 3,
    title: "The Batman",
    year: 2022,
    genre: "Hành động, Tội phạm",
    rating: 7.8,
    duration: "176 phút",
    poster:
      "https://images.unsplash.com/photo-1509347528160-9329d33b2588?ixlib=rb-4.0.3&w=400",
    video:
      "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4",
    description:
      "Trong năm thứ hai của mình với tư cách là Batman, Bruce Wayne theo đuổi những kẻ tham nhũng ở Gotham City trong khi điều tra một loạt vụ giết người tàn bạo do Riddler thực hiện.",
    category: "action",
  },
  {
    id: 4,
    title: "Dune",
    year: 2021,
    genre: "Khoa học viễn tưởng, Phiêu lưu",
    rating: 8.0,
    duration: "155 phút",
    poster:
      "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?ixlib=rb-4.0.3&w=400",
    video:
      "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4",
    description:
      "Paul Atreides, một chàng trai tài năng và xuất sắc sinh ra để làm những điều vĩ đại vượt quá sự hiểu biết của anh, phải du hành đến hành tinh nguy hiểm nhất trong vũ trụ để đảm bảo tương lai của gia đình và người dân của mình.",
    category: "sci-fi",
  },
  {
    id: 5,
    title: "Joker",
    year: 2019,
    genre: "Chính kịch, Tội phạm",
    rating: 8.4,
    duration: "122 phút",
    poster:
      "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&w=400",
    video:
      "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4",
    description:
      "Trong Gotham City những năm 1980, một diễn viên hài thất bại được đẩy đến bờ vực điên loạn và chuyển sang tội phạm. Đây là câu chuyện gốc về nhân vật phản diện mang tính biểu tượng nhất của DC.",
    category: "drama",
  },
  {
    id: 6,
    title: "Parasite",
    year: 2019,
    genre: "Chính kịch, Kinh dị",
    rating: 8.6,
    duration: "132 phút",
    poster:
      "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&w=400",
    video:
      "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4",
    description:
      "Gia đình Ki-taek nghèo khó sống trong một căn hầm bẩn thỉu. Họ quan tâm đến gia đình Park giàu có và từ từ xâm nhập vào cuộc sống của họ bằng cách trở thành gia sư và người giúp việc.",
    category: "drama",
  },
  {
    id: 7,
    title: "Phim của tôi",
    year: 2024,
    genre: "Hành động, Phiêu lưu",
    rating: 9.0,
    duration: "120 phút",
    poster: "./images/my-movie-poster.jpg",
    video: "./videos/my-movie.mp4",
    description: "Đây là bộ phim tuyệt vời mà tôi đã tạo ra...",
    category: "action",
  },
];

// DOM elements
const moviesGrid = document.getElementById("moviesGrid");
const searchInput = document.getElementById("searchInput");
const searchBtn = document.getElementById("searchBtn");
const videoModal = document.getElementById("videoModal");
const videoPlayer = document.getElementById("videoPlayer");
const movieTitle = document.getElementById("movieTitle");
const movieDescription = document.getElementById("movieDescription");
const closeModal = document.querySelector(".close");
const categoryItems = document.querySelectorAll(".category-item");
const ctaBtn = document.querySelector(".cta-btn");

// Initialize the app
document.addEventListener("DOMContentLoaded", function () {
  displayMovies(movies);
  setupEventListeners();
});

// Display movies in grid
function displayMovies(movieList) {
  moviesGrid.innerHTML = "";

  movieList.forEach((movie) => {
    const movieCard = createMovieCard(movie);
    moviesGrid.appendChild(movieCard);
  });
}

// Create movie card element
function createMovieCard(movie) {
  const movieCard = document.createElement("div");
  movieCard.className = "movie-card";
  movieCard.onclick = () => openMovie(movie);

  movieCard.innerHTML = `
        <img src="${movie.poster}" alt="${movie.title}" class="movie-poster">
        <div class="movie-info">
            <div class="movie-title">${movie.title}</div>
            <div class="movie-genre">${movie.genre}</div>
            <div class="movie-duration">${movie.duration}</div>
        </div>
    `;

  return movieCard;
}

// Open movie in modal
function openMovie(movie) {
  videoPlayer.src = movie.video;
  movieTitle.textContent = movie.title;
  movieDescription.textContent = movie.description;
  videoModal.style.display = "block";
  document.body.style.overflow = "hidden";
}

// Close modal
function closeMovieModal() {
  videoModal.style.display = "none";
  videoPlayer.pause();
  videoPlayer.src = "";
  document.body.style.overflow = "auto";
}

// Search functionality
function searchMovies() {
  const searchTerm = searchInput.value.toLowerCase().trim();

  if (searchTerm === "") {
    displayMovies(movies);
    return;
  }

  const filteredMovies = movies.filter(
    (movie) =>
      movie.title.toLowerCase().includes(searchTerm) ||
      movie.genre.toLowerCase().includes(searchTerm) ||
      movie.year.toString().includes(searchTerm)
  );

  displayMovies(filteredMovies);
}

// Filter by category
function filterByCategory(category) {
  if (category === "all") {
    displayMovies(movies);
    return;
  }

  const filteredMovies = movies.filter((movie) => movie.category === category);
  displayMovies(filteredMovies);
}

// Setup event listeners
function setupEventListeners() {
  // Search functionality
  searchBtn.addEventListener("click", searchMovies);
  searchInput.addEventListener("keypress", function (e) {
    if (e.key === "Enter") {
      searchMovies();
    }
  });

  // Modal close events
  closeModal.addEventListener("click", closeMovieModal);

  window.addEventListener("click", function (e) {
    if (e.target === videoModal) {
      closeMovieModal();
    }
  });

  // Category filtering
  categoryItems.forEach((item) => {
    item.addEventListener("click", function () {
      const category = this.getAttribute("data-genre");
      filterByCategory(category);

      // Update active category
      categoryItems.forEach((cat) => cat.classList.remove("active"));
      this.classList.add("active");
    });
  });

  // CTA button scroll to movies
  ctaBtn.addEventListener("click", function () {
    document.getElementById("movies").scrollIntoView({
      behavior: "smooth",
    });
  });

  // Smooth scrolling for navigation links
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute("href"));
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    });
  });
}
