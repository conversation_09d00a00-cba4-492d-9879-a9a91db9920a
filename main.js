var firstName = "thuan ",
  lastName = "van ";
console.log(`toi la: ${firstName}${lastName}`);
function showWhite(pa) {
  console.log(pa);
}
showWhite("toi la ai");
function nano(x) {
  return x * 5;
}
var result = nano(5);
console.log(result);
if (result > 20) {
  console.log("dieu kien dung");
}
var myString = " toi la  ";
console.log(myString.indexOf("la"));
console.log(myString.replace("toi", "minh"));
console.log(myString.toUpperCase());
console.log(myString.trim().length);
console.log(myString.split(", "));
console.log(myString.slice(3, 6));
var emailKey = "email";
var myInfo = {
  name: "van thuan",
  age: 25,
  address: "hcm",
  [emailKey]: "vanthuan.com.vn",
  getName: function () {
    return this.name;
  },
};
console.log(myInfo.getName());
// objec contrustor
function User(firstName, lastName, age, avatar) {
  this.firstName = firstName;
  this.lastName = lastName;
  this.age = age;
  this.avatar = avatar;
  this.getName = function () {
    return `${this.firstName} ${this.lastName}`;
  };
}

var author = new User("van", "thuan", 25, "avatar");
var user = new User("le", "thai", 24, "avatar");
author.title = "dhgrhgh";

console.log(author);
console.log(user.getName());

var myInfo = {
  name: "van thuan",
  age: 25,
  address: "hcm",
};
var date = new Date();
var year = date.getFullYear();
var month = date.getMonth() + 1;
var day = date.getDate();
console.log(`${day}/${month}/${year}`);

var a = 8;
var b = 6;
var result = a * b;
console.log(result);
console.log(Math.abs(-4));
console.log(Math.round(5.49));
console.log(Math.ceil);
console.log(Math.floor);
console.log(Math.random(3, 4, 5, 6, 8, 9));
console.log(Math.min);
console.log(Math.max);

var date = 3;
if (date === 2) {
  console.log("thu 2");
} else if (date === 3) {
  console.log("thu 3");
} else if (date === 4) {
  console.log("thu 4");
} else {
  console.log("khong biet");
}

switch (date) {
  case 2:
    console.log("thu 2");
    break;
  case 3:
    console.log("thu 3");
    break;
  case 4:
    console.log("thu 4");
    break;
  default:
    console.log("khong biet");
}
// for loop
//for (var i = 1; i <= 1000; i++) {
// console.log(i);
//}
var course = {
  name: "javascript",
  coin: 250,
};
var result = course.coin > 0 ? `${course.coin} coins` : "mien phi";
console.log(result);
setTimeout(function () {
  console.log("log after 1s");
}, 6000);
var a = 1;
var b = 2;
if (a > b) {
  console.log("dung");
} else if (a < b) {
  console.log("sai");
}
